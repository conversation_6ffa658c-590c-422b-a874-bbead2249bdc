import os

from ibind import Ibkr<PERSON><PERSON>, QuestionType, OrderRequest

from agi_trade import var
from agi_trade.models.signal import Signal
from agi_trade.support import slog


class IbkrConnectionHandler():
    def __init__(
        self,
        use_oauth: bool = var.USE_OAUTH,
        account_id: str = os.getenv('IBIND_ACCOUNT_ID'),
    ):
        self._use_oauth = use_oauth
        self._client = None
        self._account_id = account_id

    def connect(self):
        # TODO: decide between initialising OAuth1aConfig here, or through rather set up OAuth environment variables
        self._client = IbkrClient(
            use_oauth=self._use_oauth
        )

    @property
    def client(self) -> IbkrClient:
        if self._client is None:
            raise Exception('IBKR client not connected. Please call .connect() first.')
        return self._client

    @property
    def account_id(self):
        return self._account_id

    def check_health(self) -> bool:
        healthy = self.client.check_health()
        if healthy:
            slog.info(f'IBKR connection in good health: {self.client.check_health()}')
            return True

        slog.info(f'IBKR connection in bad health: {self.client.check_health()}')
        if self._use_oauth:
            try:
                self._client.stop_tickler()
            except Exception as e:
                slog.exception(f'Error stopping tickler.', exception=e)

            try:
                self._client.oauth_init(maintain_oauth=True, init_brokerage_session=True)
            except Exception as e:
                slog.exception(f'Error reauthenticating with OAuth.', exception=e)
        return False


def get_ledger(connection_handler: IbkrConnectionHandler):
    result = connection_handler.client.get_ledger()

    rv = {
        'cash_balance': result.data[var.CURRENCY]["cashbalance"],
        'total_portfolio_value': result.data[var.CURRENCY]["netliquidationvalue"],
        'assets_market_value': result.data[var.CURRENCY]["stockmarketvalue"],
        # TODO: add other items into the ledger as needed
        # 'other_item': result.data[var.CURRENCY]['other_item']
        # ...
    }

    return rv


def get_positions(connection_handler: IbkrConnectionHandler):
    connection_handler.client.invalidate_backend_portfolio_cache()
    result = connection_handler.client.positions2()
    rv = {}
    for position in result.data:
        symbol = position['description']

        if symbol not in rv:
            rv[symbol] = 0

        rv[symbol] += float(position['position'])

    return rv


# TODO: PLEASE REVIEW AND CONFIRM THESE ANSWERS
# TODO: add or modify the answers that you may get during the order submission. You can use string matching for previously unseen questions
_ANSWERS = {
    QuestionType.PRICE_PERCENTAGE_CONSTRAINT: True,
    QuestionType.ORDER_VALUE_LIMIT: False,
    QuestionType.MISSING_MARKET_DATA: True,
    QuestionType.STOP_ORDER_RISKS: True,
    # "For example: match for this unseen question": True
}

_SPREAD_CONIDS = {
    'AUD': '********',
    'CAD': '********',
    'CHF': '********',
    'CNH': '*********',
    'GBP': '********',
    'HKD': '********',
    'INR': '*********',
    'JPY': '********',
    'KRW': '*********',
    'MXN': '*********',
    'SEK': '*********',
    'SGD': '*********',
    'USD': '********',
}


def place_order(
    connection_handler: IbkrConnectionHandler,
    signal: Signal
):
    # TODO: add sanity checks on inputs
    account_id = connection_handler.account_id

    conidex = f"{_SPREAD_CONIDS[var.CURRENCY]};;;"

    leg_strings = []
    for leg in signal.legs:
        leg_string = (f'{leg.conid}/{leg.ratio * (1 if leg.side == "BUY" else -1)}')
        leg_strings.append(leg_string)

    conidex = conidex + ",".join(leg_strings)

    order_request = OrderRequest(
        conid=None,
        conidex=conidex,
        side=signal.side,
        quantity=signal.size,
        order_type=signal.order_type,
        price=signal.price,
        acct_id=account_id,
    )
    slog.info(f'Placing order: {order_request}')

    # result = connection_handler.client.place_order(order_requests, _ANSWERS, account_id)
    # return result
