import queue
import threading
import time

import uvicorn
from fastapi import FastAPI, HTTPException

from agi_trade import var
from agi_trade.models.signal import Signal
from agi_trade.support import slog


class RestServer:
    def __init__(
        self,
        input_queue: queue.Queue,
        output_queue: queue.Queue,
        host=var.REST_SERVER_HOST,
        port=var.REST_SERVER_PORT
        ):
        self._input_queue = input_queue
        self._output_queue = output_queue
        self._host = host
        self._port = port
        self._app = FastAPI()
        self._setup_routes()
        self._server = None
        self._thread = None

    def _setup_routes(self):
        @self._app.post("/new_signal")
        async def new_signal(signal: Signal):
            try:
                self._input_queue.put_nowait(signal)
                return {"status": "queued", "coid": signal.coid or "n/a"}
            except queue.Full:
                raise HTTPException(status_code=503, detail="Queue is full")

        @self._app.get("/healthz")
        async def health():
            return {"status": "ok", "timestamp": time.time()}

        @self._app.get("/empty")
        async def empty():
            return {"status": "ok", "empty": self._input_queue.empty()}

        @self._app.get("/outputs")
        async def outputs():
            outputs = []
            while True:
                if self._output_queue.empty():
                    break

                try:
                    outputs.append(self._output_queue.get_nowait())
                except queue.Empty:
                    break
                except Exception as e:
                    slog.exception(f'Exception getting output from queue', exception=e)

            return {"status": "ok", "outputs": outputs}

    def start(self):
        if self._thread is None or not self._thread.is_alive():
            self._thread = threading.Thread(target=self._worker, daemon=True)
            self._thread.start()

    def _worker(self):
        slog.info(f"{self} started.")
        config = uvicorn.Config(self._app, host=self._host, port=self._port, log_level="warning")
        server = uvicorn.Server(config)
        self._server = server
        server.run()

    def check_health(self):
        return self._thread is not None and self._thread.is_alive()

    def stop(self):
        slog.info(f"{self} Stopping...")
        if self._server is not None:
            self._server.should_exit = True
        if self._thread is not None:
            self._thread.join(timeout=15)
        self._thread = None

    def __str__(self):
        return f"RestServer({self._host}:{self._port})"
