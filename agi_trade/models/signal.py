from typing import Optional, List

from pydantic import BaseModel

from agi_trade.support.py_utils import VerboseEnum


class SignalIntent(VerboseEnum):
    IGNORE = 'ignore'
    PLACE_ORDER = 'place_order'
    CANCEL_ORDER = 'cancel_order'


# TODO: these are temporary, we'll define them better later

class Leg(BaseModel):
    symbol: str
    conid: Optional[int] = None
    ratio: float
    side: str


class Signal(BaseModel):
    symbol: str
    conid: Optional[int] = None
    expiry: str
    legs: List[Leg]
    price: float
    side: str
    order_type: str
    size: float
    coid: str = None
    intent: SignalIntent = None

