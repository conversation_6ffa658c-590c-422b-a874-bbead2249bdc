import queue
import time

from agi_trade import var
from agi_trade.adapters.ibkr.ibkr_handlers import IbkrConnectionHandler
from agi_trade import __version__
from agi_trade.core.execution_controller import ExecutionController
from agi_trade.core.signal_processor import SignalProcessor
from agi_trade.server.rest_server import RestServer
from agi_trade.support import slog


class App():
    def __init__(self):
        self._running = False

        self.setup()

    def setup(self):

        self._connection_handler = IbkrConnectionHandler()
        self._signal_processor = SignalProcessor(self._connection_handler)

        self._input_queue = queue.Queue()
        self._output_queue = queue.Queue()

        self._execution_controller = ExecutionController(
            connection_handler=self._connection_handler,
            signal_processor=self._signal_processor,
            input_queue=self._input_queue,
            output_queue=self._output_queue,
        )

        self._rest_server = RestServer(
            input_queue=self._input_queue,
            output_queue=self._output_queue
        )

    def _maintain(self):
        try:
            if not self._execution_controller.is_alive:
                if self._execution_controller.running:
                    self._execution_controller.stop()
                slog.info(f"{self._execution_controller} is stopped, attempting to start")
                self._execution_controller.start()
        except Exception as e:
            slog.exception(f'Exception maintaining {self._execution_controller}.', exception=e)

        try:
            if not self._rest_server.check_health():
                slog.info(f"{self._rest_server} is stopped, attempting to start")
                self._rest_server.start()
        except Exception as e:
            slog.exception(f'Exception maintaining {self._execution_controller}.', exception=e)

    def start(self):
        slog.info("Starting App")

        if self._running:
            return

        self._running = True
        try:
            slog.info("## App started ##")

            try:
                while self._running:
                    self._maintain()
                    time.sleep(0.1)
            except KeyboardInterrupt:
                slog.info('Keyboard interrupt, shutting down.')
                pass
            finally:
                slog.info("App shutting down")
        except Exception as e:
            slog.exception(f'Exception starting the app', exception=e)

        self.shutdown()

    def stop(self):
        slog.info("Stopping App")
        self._running = False

    def shutdown(self):
        self._execution_controller.stop()
        self._rest_server.stop()


def main_app():
    slog.info(f'###### Starting {var.PROJECT} v{__version__} ({var.FULL_NAME}) ######')
    slog.info(f'{var.all_variables}')
    app = None
    try:
        app = App()
    except Exception as e:
        slog.critical(f"Exception while creating the app: {e}", exc_info=True)
        if app is not None:
            app.shutdown()
        return

    try:
        app.start()
    except Exception as e:
        slog.critical(f"Exception while starting the app: {e}", exc_info=True)
        app.shutdown()
