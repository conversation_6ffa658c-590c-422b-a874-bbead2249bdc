from pydantic import BaseModel, Field

from agi_trade.support.datetime_utils import utc_now


class Datastore(BaseModel):
    ledger: dict = Field(default_factory=dict)  # noqa
    positions: dict = Field(default_factory=dict)  # noqa

    def update_ledger(self, new_ledger: dict):
        self.ledger = {**new_ledger, '_updated_at': utc_now()}

    def update_positions(self, new_positions: dict):
        self.positions = {**new_positions, '_updated_at': utc_now()}
