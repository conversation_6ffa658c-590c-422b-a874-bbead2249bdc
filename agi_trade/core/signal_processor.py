from agi_trade.adapters.ibkr.ibkr_handlers import IbkrConnectionHandler
from agi_trade.core.datastore import Datastore
from agi_trade.models.signal import Signal, Leg
from agi_trade.support import slog


class SignalProcessor():

    def __init__(
        self,
        connection_handler: IbkrConnectionHandler,

    ):
        self._connection_handler = connection_handler

    def make_legs(self, signal):
        processed_legs = []
        for leg_data in signal.legs:
            print(leg_data)
            leg = Leg(
                symbol=leg_data['symbol'],
                conid=leg_data['conid'],
                ratio=leg_data['ratio'],
                side=leg_data['side'],
            )
            processed_legs.append(leg)
        signal.legs = processed_legs

    def process(self, signal: Signal, datastore: Datastore):
        slog.info(f'Processing signal: {signal}')

        # TODO: pull strike data, implement the dynamic pricing, etc.

        ...

        """ 
        Leaving the code below to help getting started with pulling options data.
        
        Use datastore object to access positions and ledger.
         
        """
        # symbol = signal.symbol
        # self._connection_handler.client.search_contract_by_symbol(symbol)  # to warm up secdef
        # conid = self._connection_handler.client.stock_conid_by_symbol(symbol).data[symbol]
        # print(conid)
        # strikes = self._connection_handler.client.search_strikes_by_conid(conid, sec_type="OPT", month="JUL25").data
        # print(strikes)
        #
        # option_conid = self._connection_handler.client.search_secdef_info_by_conid(
        #     conid=conid,
        #     sec_type="OPT",
        #     month="JUL25",
        #     strike="100",
        #     right="C"  # "P" for put
        # ).data[0]['conid']
        # print(option_conid)
        # option_info = self._connection_handler.client.contract_information_by_conid(option_conid).data
        # print(option_info)
        # self._connection_handler.client.live_marketdata_snapshot([str(option_conid)], ["31", "84", "86", "87"]) # call twice due to preflight
        # marketdata = self._connection_handler.client.live_marketdata_snapshot([str(option_conid)], ["31", "84", "86", "87"]).data  # Last, Bid, Ask, Volume
        # print(marketdata)
