import queue
import time
from collections import deque

from superloops import <PERSON><PERSON>oop

from agi_trade.adapters.ibkr import ibkr_handlers
from agi_trade.adapters.ibkr.ibkr_handlers import IbkrConnectionHandler
from agi_trade.core.datastore import Datastore
from agi_trade.core.signal_processor import SignalProcessor
from agi_trade.models.signal import Signal, SignalIntent
from agi_trade.support import slog


class ExecutionController(SuperLoop):

    def __init__(
        self,
        connection_handler: IbkrConnectionHandler,
        signal_processor: SignalProcessor,
        input_queue: queue.Queue,
        output_queue: queue.Queue,
        loop_delay: float = 1,
        ):
        super().__init__()
        self._connection_handler = connection_handler
        self._signal_processor = signal_processor
        self._input_queue = input_queue
        self._output_queue = output_queue
        self._loop_delay = loop_delay

        self._input_signals = deque()
        self._processed_signals = deque()
        self._datastore = Datastore()
        self.setup()

    def setup(self):
        self._connection_handler.connect()

    def _get_order_updates(self):
        ...

    def _get_ledger(self):
        try:
            ledger = ibkr_handlers.get_ledger(self._connection_handler)
            self._datastore.update_ledger(ledger)
        except Exception as e:
            slog.exception(f'Error fetching ledger.', exception=e)

    def _get_positions(self):
        try:
            positions = ibkr_handlers.get_positions(self._connection_handler)
            self._datastore.update_positions(positions)
        except Exception as e:
            slog.exception(f'Error fetching positions.', exception=e)

    def _maintenance(self):
        self._get_order_updates()
        self._get_positions()
        self._get_ledger()

    def _check_signal_queue(self):
        while True:
            if self._input_queue.empty():
                break
            try:
                signal = self._input_queue.get_nowait()
                self._input_signals.append(signal)
            except queue.Empty:
                break
            except Exception as e:
                slog.exception(f'Exception getting signal from queue', exception=e)

    def _pre_process_signal(self):
        for signal in self._input_signals:
            self._signal_processor.process(signal, self._datastore.model_copy(deep=True))
            self._processed_signals.append(signal)

    def _place_order(self, signal: Signal):
        ibkr_handlers.place_order(self._connection_handler, signal)

    def _cancel_order(self, signal: Signal):
        ...

    def _handle_signal(self, signal: Signal):
        if signal.intent == SignalIntent.PLACE_ORDER:
            self._place_order(signal)
        elif signal.intent == SignalIntent.CANCEL_ORDER:
            self._cancel_order(signal)
        elif signal.intent == SignalIntent.IGNORE:
            pass  # do nothing on purpose, but mark signal as processed
        # TODO: add more elif clauses here for different intents
        else:
            slog.error(f'Unknown intent: {signal.intent} for signal: {signal}')
        self._output_queue.put_nowait(signal)

    def _handle_processed_signals(self):
        for signal in self._processed_signals:
            try:
                self._handle_signal(signal)
            except Exception as e:
                slog.exception(f'Exception handling signal: {signal}', exception=e)

    def cycle(self):
        # TODO: wrap in try/except clauses

        if not self._connection_handler.check_health():
            return False

        self._maintenance()
        self._check_signal_queue()
        self._pre_process_signal()
        self._handle_processed_signals()

        # TODO: implement other actions

        time.sleep(self._loop_delay)
